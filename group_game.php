<?php
// فایل مدیریت بازی‌های گروهی (3 نفره)

//تابع اضافه کردن کاربر به صف جستجوی بازی 3 نفره:
function addToThreePlayerQueue($user_id) {
    $queueFile = "data/three_player_queue.json";
    $queue = [];

    if (file_exists($queueFile)) {
        $queue = json_decode(file_get_contents($queueFile), true);
        if (!$queue) $queue = [];
    }

    // حذف کاربر از صف در صورت وجود قبلی
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            unset($queue[$index]);
        }
    }

    // دریافت اطلاعات کاربر
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    $userGender = isset($userData['userfild']['gender']) ? $userData['userfild']['gender'] : '';
    $userNickname = !empty($userData['userfild']['nickname']) ? $userData['userfild']['nickname'] : "ناشناس";

    // اضافه کردن کاربر جدید به صف (جنسیت مهم نیست - رندوم)
    $queue[] = [
        'user_id' => $user_id,
        'timestamp' => time(),
        'user_gender' => $userGender,
        'nickname' => $userNickname
    ];

    file_put_contents($queueFile, json_encode($queue, JSON_PRETTY_PRINT));

    // تلاش برای پیدا کردن گروه 3 نفره
    findThreePlayerOpponents($user_id);
}

//تابع حذف کاربر از صف جستجوی بازی 3 نفره:
function removeFromThreePlayerQueue($user_id) {
    $queueFile = "data/three_player_queue.json";
    if (!file_exists($queueFile)) {
        return false;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue) return false;

    // حذف کاربر از صف
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            unset($queue[$index]);
        }
    }

    file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));
    return true;
}

//تابع پیدا کردن حریفان برای بازی 3 نفره:
function findThreePlayerOpponents($user_id) {
    $queueFile = "data/three_player_queue.json";
    if (!file_exists($queueFile)) {
        return false;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue || count($queue) < 3) {
        return false; // کمتر از 3 نفر در صف
    }

    // پیدا کردن کاربر فعلی در صف
    $currentUserIndex = -1;
    $currentUser = null;
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            $currentUserIndex = $index;
            $currentUser = $item;
            break;
        }
    }

    if ($currentUserIndex == -1 || !$currentUser) {
        return false;
    }

    // جمع‌آوری 2 بازیکن دیگر (رندوم - جنسیت مهم نیست)
    $suitablePlayers = [];
    $suitableIndexes = [];

    foreach ($queue as $index => $item) {
        if ($index != $currentUserIndex && 
            $item['user_id'] != $user_id) {
            
            // بررسی اینکه کاربر هنوز آنلاین است و در بازی نیست
            $playerFile = "data/user/{$item['user_id']}.json";
            if (file_exists($playerFile)) {
                $playerData = json_decode(file_get_contents($playerFile), true);
                // بررسی اینکه کاربر در بازی نباشد
                if (!isset($playerData['userfild']['ingame']) || $playerData['userfild']['ingame'] != 'on') {
                    $suitablePlayers[] = $item;
                    $suitableIndexes[] = $index;
                    
                    // اگر 2 بازیکن پیدا شد، بازی را شروع کن
                    if (count($suitablePlayers) >= 2) {
                        break;
                    }
                }
            }
        }
    }

    // اگر 2 بازیکن مناسب پیدا شد
    if (count($suitablePlayers) >= 2) {
        // اضافه کردن کاربر فعلی به لیست بازیکنان
        array_unshift($suitablePlayers, $currentUser);
        array_unshift($suitableIndexes, $currentUserIndex);

        // حذف همه 3 بازیکن از صف
        $indexesToRemove = array_slice($suitableIndexes, 0, 3);
        rsort($indexesToRemove); // مرتب کردن از بزرگ به کوچک برای حذف صحیح
        
        foreach ($indexesToRemove as $indexToRemove) {
            unset($queue[$indexToRemove]);
        }
        
        file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));

        // شروع بازی 3 نفره
        startThreePlayerGame(array_slice($suitablePlayers, 0, 3));
        return true;
    }

    return false;
}

//تابع شروع بازی 3 نفره:
function startThreePlayerGame($players, $isFriendlyGame = false) {
    $playerIds = [];
    $playerNicknames = [];
    $playerTokens = [];
    
    // جمع‌آوری اطلاعات بازیکنان
    foreach ($players as $player) {
        $playerId = $player['user_id'];
        $playerIds[] = $playerId;
        
        // دریافت اطلاعات کامل بازیکن
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[] = !empty($playerData['userfild']['nickname']) ? $playerData['userfild']['nickname'] : "ناشناس";
        $playerTokens[] = isset($playerData['userfild']['user_token']) ? $playerData['userfild']['user_token'] : "نامشخص";
    }

    // کسر سکه از همه بازیکنان (2 سکه برای بازی عادی، بازی دوستانه رایگان است)
    $coinCost = 2; // پیش‌فرض برای بازی عادی

    $failedPlayers = [];

    // فقط در بازی‌های عادی (غیر دوستانه) سکه کسر می‌شود
    if (!$isFriendlyGame) {
        foreach ($playerIds as $playerId) {
            if (!hasActiveSubscription($playerId)) {
                if (!checkAndDeductCoins($playerId, $coinCost)) {
                    $failedPlayers[] = $playerId;
                }
            }
        }
    }

    // اگر کسی سکه کافی نداشت، همه را به صف برگردان
    if (!empty($failedPlayers)) {
        // بازگرداندن سکه بازیکنان موفق
        foreach ($playerIds as $playerId) {
            if (!in_array($playerId, $failedPlayers) && !hasActiveSubscription($playerId)) {
                refundCoins($playerId, $coinCost);
            }
        }
        
        // بازگرداندن همه به صف
        foreach ($players as $player) {
            addToThreePlayerQueue($player['user_id']);
        }
        return false;
    }

    // ایجاد گروه بازی
    $gameId = 'three_player_' . time() . '_' . rand(1000, 9999);
    createThreePlayerGameSession($gameId, $playerIds);

    // ارسال پیام به همه بازیکنان
    sendThreePlayerGameStartMessages($playerIds, $playerNicknames, $playerTokens, $gameId);

    // قرعه‌کشی برای تعیین نوبت اول
    $firstPlayerIndex = array_rand($playerIds);
    $firstPlayerId = $playerIds[$firstPlayerIndex];
    
    // ارسال پیام انتخاب جرعت/حقیقت به بازیکن اول
    sendFirstPlayerChoice($firstPlayerId, $gameId);

    return true;
}

//تابع ایجاد جلسه بازی 3 نفره:
function createThreePlayerGameSession($gameId, $playerIds) {
    $gameData = [
        'game_id' => $gameId,
        'players' => $playerIds,
        'player_count' => 3,
        'current_turn' => 0,
        'game_state' => 'waiting_for_choice',
        'created_at' => time(),
        'round' => 1,
        'current_questioner' => null,
        'current_answerer' => null,
        'current_observer' => null,
        'current_choice' => null,
        'current_question' => null
    ];

    // ذخیره اطلاعات بازی
    if (!file_exists("data/three_player_games")) {
        mkdir("data/three_player_games", 0777, true);
    }
    
    file_put_contents("data/three_player_games/$gameId.json", json_encode($gameData, JSON_PRETTY_PRINT));

    // تنظیم وضعیت بازیکنان
    foreach ($playerIds as $playerId) {
        $userData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $userData['userfild']['ingame'] = 'on';
        $userData['userfild']['three_player_game_id'] = $gameId;
        file_put_contents("data/user/$playerId.json", json_encode($userData, true));
        
        // تنظیم زمان شروع بازی
        setGameStartTime($playerId);
    }
}

//تابع ارسال پیام شروع بازی 3 نفره:
function sendThreePlayerGameStartMessages($playerIds, $playerNicknames, $playerTokens, $gameId) {
    // ویرایش پیام جستجو به "پایان جستجو" برای همه بازیکنان
    foreach ($playerIds as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        if (isset($playerData['userfild']['three_player_search_message_id'])) {
            jijibot('editmessagetext',[
                'chat_id' => $playerId,
                'message_id' => $playerData['userfild']['three_player_search_message_id'],
                'text' => "🔔 پایان جستجو

<blockquote>⚠️ هشدار سیستم : کاربر گرامی به هیچ وجه اطلاعات تماس خود را در اختیار افراد قرار ندهید.</blockquote>",
                'parse_mode' => 'HTML'
            ]);
            unset($playerData['userfild']['three_player_search_message_id']);
            file_put_contents("data/user/$playerId.json", json_encode($playerData, true));
        }
    }

    $playersList = "";
    for ($i = 0; $i < count($playerIds); $i++) {
        $playersList .= "✱ " . $playerNicknames[$i] . " (<a href=\"https://t.me/jdarebot?start=" . $playerTokens[$i] . "\">" . $playerTokens[$i] . "</a>)\n";
    }

    $coinCost = $isFriendlyGame ? 3 : 2;
    $gameType = $isFriendlyGame ? "دوستانه " : "";

    foreach ($playerIds as $index => $playerId) {
        $hasSubscription = hasActiveSubscription($playerId);

        if ($isFriendlyGame) {
            $coinMessage = "🎁 بازی دوستانه رایگان است - سکه‌ای کسر نشد.";
        } else {
            $coinMessage = $hasSubscription ?
                "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
                "💰 $coinCost سکه از حساب شما کسر شد.";
        }

        jijibot('sendmessage',[
            'chat_id' => $playerId,
            'text' => "🔥 بازی {$gameType}3 نفره شروع شد!

👥 بازیکنان:
$playersList
✱ $coinMessage

✱ در حال پردازش بازی...

✱ ربات در حال قرعه کشی برای بازی می باشد.",
            'parse_mode' => 'HTML',
            'disable_web_page_preview' => true,
            'reply_markup' => json_encode([
                'keyboard' => [
                    [
                        ['text' => "👥 مشاهده بازیکنان"],
                        ['text' => "❌ ترک بازی"]
                    ],
                ],
                'resize_keyboard' => true
            ])
        ]);
    }
}

//تابع ارسال انتخاب به بازیکن اول (به‌روزرسانی شده):
function sendFirstPlayerChoice($playerId, $gameId) {
    // قرعه‌کشی برای تعیین نقش‌ها
    $roles = assignThreePlayerRoles($gameId);
    if (!$roles) return false;

    // ارسال پیام‌های نقش به همه بازیکنان
    sendThreePlayerRoleMessages($gameId, $roles);

    return true;
}

//تابع دریافت اطلاعات بازی 3 نفره:
function getThreePlayerGameData($gameId) {
    $gameFile = "data/three_player_games/$gameId.json";
    if (!file_exists($gameFile)) {
        return null;
    }
    return json_decode(file_get_contents($gameFile), true);
}

//تابع به‌روزرسانی اطلاعات بازی 3 نفره:
function updateThreePlayerGameData($gameId, $gameData) {
    $gameFile = "data/three_player_games/$gameId.json";

    // بررسی وجود دایرکتوری
    if (!file_exists("data/three_player_games")) {
        mkdir("data/three_player_games", 0777, true);
    }

    $result = file_put_contents($gameFile, json_encode($gameData, JSON_PRETTY_PRINT));
    if ($result === false) {
        error_log("updateThreePlayerGameData: Failed to write game data for gameId: $gameId");
        return false;
    }

    return true;
}

//تابع پایان بازی 3 نفره:
function endThreePlayerGame($gameId, $reason = 'completed') {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) return false;

    $playerIds = $gameData['players'];

    // تنظیم وضعیت بازیکنان
    foreach ($playerIds as $playerId) {
        $userFile = "data/user/$playerId.json";
        if (file_exists($userFile)) {
            $userData = json_decode(file_get_contents($userFile), true);
            $userData['userfild']['ingame'] = 'off';
            unset($userData['userfild']['three_player_game_id']);
            file_put_contents($userFile, json_encode($userData, true));
        }
    }

    // حذف فایل بازی
    $gameFile = "data/three_player_games/$gameId.json";
    if (file_exists($gameFile)) {
        unlink($gameFile);
    }

    return true;
}

//تابع بررسی اینکه آیا کاربر در بازی 3 نفره است:
function isUserInThreePlayerGame($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    return isset($userData['userfild']['three_player_game_id']) &&
           !empty($userData['userfild']['three_player_game_id']);
}

//تابع دریافت شناسه بازی 3 نفره کاربر:
function getUserThreePlayerGameId($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return null;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    return isset($userData['userfild']['three_player_game_id']) ?
           $userData['userfild']['three_player_game_id'] : null;
}

//تابع ارسال پیام پایان بازی به همه بازیکنان:
function sendThreePlayerGameEndMessages($playerIds, $reason = 'completed', $leavingPlayerNickname = null) {
    foreach($playerIds as $playerId) {
        // بررسی زمان بازی برای بازگرداندن سکه
        $shouldRefundCoins = !isGameLongerThan2Minutes($playerId);

        // پیام اول: بستن دکمه‌ها + اطلاع‌رسانی سکه
        if ($shouldRefundCoins) {
            if (!hasActiveSubscription($playerId)) {
                refundCoins($playerId, 2);
                $coinMessage = "💳 سکه

به علت پایان 2 سکه مصرف شده در این بازی به حساب شما برگردانده شد.";
            } else {
                $coinMessage = "💳 سکه

به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشده بود.";
            }
        } else {
            $coinMessage = "💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.";
        }

        jijibot('sendmessage',[
            'chat_id'=>$playerId,
            'text'=>$coinMessage,
            'reply_markup'=>json_encode([
                'remove_keyboard'=>true
            ])
        ]);

        // پیام دوم: اعلام پایان بازی + دکمه منوی اصلی
        $endMessage = "";
        switch($reason) {
            case 'player_left':
                $endMessage = "🚪 بازی 3 نفره به دلیل خروج $leavingPlayerNickname پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
                break;
            case 'completed':
                $endMessage = "🎉 بازی 3 نفره با موفقیت به پایان رسید!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
                break;
            default:
                $endMessage = "🔚 بازی 3 نفره پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
        }

        jijibot('sendmessage',[
            'chat_id'=>$playerId,
            'text'=>$endMessage,
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                    ],
                ]
            ])
        ]);
    }
}

//تابع قرعه‌کشی برای تعیین نقش‌های بازیکنان:
function assignThreePlayerRoles($gameId) {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) {
        error_log("assignThreePlayerRoles: Game data not found for gameId: $gameId");
        return false;
    }

    $players = $gameData['players'];
    if (count($players) != 3) {
        error_log("assignThreePlayerRoles: Invalid player count: " . count($players));
        return false;
    }

    // قرعه‌کشی برای تعیین سوال‌کننده
    $questionerIndex = array_rand($players);
    $questioner = $players[$questionerIndex];

    // حذف سوال‌کننده از لیست برای انتخاب پاسخ‌دهنده
    $remainingPlayers = array_values(array_diff($players, [$questioner]));

    // قرعه‌کشی برای تعیین پاسخ‌دهنده از 2 نفر باقی‌مانده
    $answererIndex = array_rand($remainingPlayers);
    $answerer = $remainingPlayers[$answererIndex];

    // نفر سوم به عنوان مشاهده‌گر
    $observer = array_values(array_diff($remainingPlayers, [$answerer]))[0];

    // به‌روزرسانی اطلاعات بازی
    $gameData['current_questioner'] = $questioner;
    $gameData['current_answerer'] = $answerer;
    $gameData['current_observer'] = $observer;
    $gameData['game_state'] = 'waiting_for_choice';
    $gameData['current_choice'] = null;
    $gameData['current_question'] = null;

    $updateResult = updateThreePlayerGameData($gameId, $gameData);
    if (!$updateResult) {
        error_log("assignThreePlayerRoles: Failed to update game data for gameId: $gameId");
        return false;
    }

    return [
        'questioner' => $questioner,
        'answerer' => $answerer,
        'observer' => $observer
    ];
}

//تابع ارسال پیام‌های نقش به بازیکنان:
function sendThreePlayerRoleMessages($gameId, $roles) {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) return false;

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $questionerNickname = $playerNicknames[$roles['questioner']];
    $answererNickname = $playerNicknames[$roles['answerer']];
    $observerNickname = $playerNicknames[$roles['observer']];

    // پیام برای سوال‌کننده - منتظر انتخاب پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id' => $roles['questioner'],
        'text' => "🎯 نقش شما: سوال‌کننده

⏳ منتظر انتخاب $answererNickname باشید...

✱ پاسخ‌دهنده: $answererNickname
✱ مشاهده‌گر: $observerNickname

✱ شما می‌توانید با مشاهده‌گر چت کنید (تا قبل از شروع سوال)
✱ بعد از انتخاب پاسخ‌دهنده، شما سوال خواهید پرسید.",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // پیام برای پاسخ‌دهنده - او باید جرعت/حقیقت انتخاب کند
    jijibot('sendmessage',[
        'chat_id' => $roles['answerer'],
        'text' => "🎯 نقش شما: پاسخ‌دهنده

✨ نوبت شما است که انتخاب کنید!

✱ سوال‌کننده: $questionerNickname
✱ مشاهده‌گر: $observerNickname

لطفا یکی را انتخاب کنید:",
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "🔥 جرعت", 'callback_data' => "three_dare_$gameId"],
                    ['text' => "🗣 حقیقت", 'callback_data' => "three_truth_$gameId"]
                ],
            ]
        ])
    ]);

    // پیام برای مشاهده‌گر
    jijibot('sendmessage',[
        'chat_id' => $roles['observer'],
        'text' => "✨ نقش شما: مشاهده‌گر

👀 شما در این دور مشاهده‌گر هستید.

✱ سوال‌کننده: $questionerNickname
✱ پاسخ‌دهنده: $answererNickname

✱ شما می‌توانید با سوال‌کننده چت کنید
✱ منتظر انتخاب $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    return true;
}

//تابع شروع دور جدید بازی:
function startNewThreePlayerRound($gameId) {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) return false;

    // افزایش شماره دور
    $gameData['round']++;
    updateThreePlayerGameData($gameId, $gameData);

    // قرعه‌کشی جدید برای نقش‌ها
    $roles = assignThreePlayerRoles($gameId);
    if (!$roles) return false;

    // ارسال پیام شروع دور جدید
    foreach ($gameData['players'] as $playerId) {
        jijibot('sendmessage',[
            'chat_id' => $playerId,
            'text' => "🎲 دور جدید شروع شد!

🔄 در حال قرعه‌کشی برای تعیین نقش‌های جدید...

✱ دور: " . $gameData['round'],
            'reply_markup' => json_encode([
                'keyboard' => [
                    [
                        ['text' => "👥 مشاهده بازیکنان"],
                        ['text' => "❌ ترک بازی"]
                    ],
                ],
                'resize_keyboard' => true
            ])
        ]);
    }

    // ارسال پیام‌های نقش
    sendThreePlayerRoleMessages($gameId, $roles);

    return true;
}

//تابع دریافت نقش کاربر در بازی:
function getUserRoleInThreePlayerGame($userId, $gameId) {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) return null;

    if ($userId == $gameData['current_questioner']) {
        return 'questioner';
    } elseif ($userId == $gameData['current_answerer']) {
        return 'answerer';
    } elseif ($userId == $gameData['current_observer']) {
        return 'observer';
    }

    return null;
}

//تابع دریافت آمار بازی 3 نفره:
function getThreePlayerGameStats($gameId) {
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) return null;

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    return [
        'round' => $gameData['round'],
        'state' => $gameData['game_state'],
        'questioner' => $playerNicknames[$gameData['current_questioner']] ?? 'نامشخص',
        'answerer' => $playerNicknames[$gameData['current_answerer']] ?? 'نامشخص',
        'observer' => $playerNicknames[$gameData['current_observer']] ?? 'نامشخص',
        'choice' => $gameData['current_choice'] ?? null,
        'question' => $gameData['current_question'] ?? null
    ];
}

//تابع پاکسازی بازی‌های قدیمی (بیش از 30 دقیقه):
function cleanupOldThreePlayerGames() {
    $gamesDir = "data/three_player_games";
    if (!file_exists($gamesDir)) return;

    $files = glob("$gamesDir/*.json");
    $currentTime = time();
    $maxAge = 30 * 60; // 30 دقیقه

    foreach ($files as $file) {
        $gameData = json_decode(file_get_contents($file), true);
        if ($gameData && isset($gameData['created_at'])) {
            $gameAge = $currentTime - $gameData['created_at'];
            if ($gameAge > $maxAge) {
                // اطلاع‌رسانی به بازیکنان
                foreach ($gameData['players'] as $playerId) {
                    jijibot('sendmessage',[
                        'chat_id' => $playerId,
                        'text' => "⏰ بازی 3 نفره به دلیل عدم فعالیت پایان یافت!

بازی بیش از 30 دقیقه غیرفعال بود.",
                        'reply_markup' => json_encode([
                            'remove_keyboard' => true
                        ])
                    ]);

                    // پاک کردن اطلاعات بازی از کاربر
                    $userFile = "data/user/$playerId.json";
                    if (file_exists($userFile)) {
                        $userData = json_decode(file_get_contents($userFile), true);
                        $userData['userfild']['ingame'] = 'off';
                        $userData['userfild']['step'] = 'none';
                        unset($userData['userfild']['three_player_game_id']);
                        file_put_contents($userFile, json_encode($userData, true));
                    }
                }

                // حذف فایل بازی
                unlink($file);
            }
        }
    }
}

//تابع بررسی محتوای نامناسب (ساده):
function isContentAppropriate($text) {
    if (empty($text)) return true;

    // لیست کلمات ممنوع (می‌توان گسترش داد)
    $bannedWords = [
        'کلمه_ممنوع1', 'کلمه_ممنوع2' // اینجا کلمات واقعی ممنوع را اضافه کنید
    ];

    $text = strtolower($text);
    foreach ($bannedWords as $word) {
        if (strpos($text, $word) !== false) {
            return false;
        }
    }

    return true;
}

//تابع ارسال پیام هشدار محتوای نامناسب:
function sendInappropriateContentWarning($userId) {
    jijibot('sendmessage',[
        'chat_id' => $userId,
        'text' => "⚠️ محتوای نامناسب

لطفا از ارسال محتوای نامناسب خودداری کنید.
در صورت تکرار، حساب شما مسدود خواهد شد.",
    ]);
}

//تابع نمایش وضعیت فعلی بازی برای کاربر:
function showThreePlayerGameStatus($userId) {
    $gameId = getUserThreePlayerGameId($userId);
    if (!$gameId) {
        jijibot('sendmessage',[
            'chat_id' => $userId,
            'text' => "❌ شما در حال حاضر در بازی 3 نفره نیستید!",
        ]);
        return false;
    }

    $gameStats = getThreePlayerGameStats($gameId);
    if (!$gameStats) {
        jijibot('sendmessage',[
            'chat_id' => $userId,
            'text' => "❌ اطلاعات بازی یافت نشد!",
        ]);
        return false;
    }

    $userRole = getUserRoleInThreePlayerGame($userId, $gameId);
    $roleText = "";
    switch($userRole) {
        case 'questioner':
            $roleText = "🎯 سوال‌کننده";
            break;
        case 'answerer':
            $roleText = "🎯 پاسخ‌دهنده";
            break;
        case 'observer':
            $roleText = "👀 مشاهده‌گر";
            break;
        default:
            $roleText = "❓ نامشخص";
    }

    $stateText = "";
    switch($gameStats['state']) {
        case 'waiting_for_choice':
            $stateText = "⏳ منتظر انتخاب جرعت/حقیقت";
            break;
        case 'waiting_for_question':
            $stateText = "⏳ منتظر سوال";
            break;
        case 'waiting_for_answer':
            $stateText = "⏳ منتظر پاسخ";
            break;
        default:
            $stateText = "❓ نامشخص";
    }

    $statusMessage = "📊 وضعیت بازی 3 نفره

🎮 دور: {$gameStats['round']}
$roleText

👥 بازیکنان:
🎯 سوال‌کننده: {$gameStats['questioner']}
🎯 پاسخ‌دهنده: {$gameStats['answerer']}
👀 مشاهده‌گر: {$gameStats['observer']}

📋 وضعیت: $stateText";

    if ($gameStats['choice']) {
        $choiceText = $gameStats['choice'] == 'dare' ? 'جرعت' : 'حقیقت';
        $statusMessage .= "\n💪🏻 انتخاب شده: $choiceText";
    }

    jijibot('sendmessage',[
        'chat_id' => $userId,
        'text' => $statusMessage,
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    return true;
}









//تابع اضافه کردن کاربر به صف جستجوی بازی 4 نفره:
function addToFourPlayerQueue($user_id) {
    $queueFile = "data/four_player_queue.json";
    $queue = [];

    if (file_exists($queueFile)) {
        $queue = json_decode(file_get_contents($queueFile), true);
        if (!$queue) $queue = [];
    }

    // حذف کاربر از صف در صورت وجود قبلی
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            unset($queue[$index]);
        }
    }

    // دریافت اطلاعات کاربر
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    $userGender = isset($userData['userfild']['gender']) ? $userData['userfild']['gender'] : '';
    $userNickname = !empty($userData['userfild']['nickname']) ? $userData['userfild']['nickname'] : "ناشناس";

    // اضافه کردن کاربر جدید به صف (جنسیت مهم نیست - رندوم)
    $queue[] = [
        'user_id' => $user_id,
        'timestamp' => time(),
        'user_gender' => $userGender,
        'nickname' => $userNickname
    ];

    file_put_contents($queueFile, json_encode($queue, JSON_PRETTY_PRINT));

    // تلاش برای پیدا کردن گروه 4 نفره
    findFourPlayerOpponents($user_id);
}

//تابع حذف کاربر از صف جستجوی بازی 4 نفره:
function removeFromFourPlayerQueue($user_id) {
    $queueFile = "data/four_player_queue.json";
    if (!file_exists($queueFile)) {
        return false;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue) return false;

    // حذف کاربر از صف
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            unset($queue[$index]);
        }
    }

    file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));
    return true;
}

//تابع پیدا کردن حریفان برای بازی 4 نفره:
function findFourPlayerOpponents($user_id) {
    $queueFile = "data/four_player_queue.json";
    if (!file_exists($queueFile)) {
        return false;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue || count($queue) < 4) {
        return false; // کمتر از 4 نفر در صف
    }

    // پیدا کردن کاربر فعلی در صف
    $currentUserIndex = -1;
    $currentUser = null;
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            $currentUserIndex = $index;
            $currentUser = $item;
            break;
        }
    }

    if ($currentUserIndex == -1 || !$currentUser) {
        return false;
    }

    // جمع‌آوری 3 بازیکن دیگر (رندوم - جنسیت مهم نیست)
    $suitablePlayers = [];
    $suitableIndexes = [];

    foreach ($queue as $index => $item) {
        if ($index != $currentUserIndex &&
            $item['user_id'] != $user_id) {

            // بررسی اینکه کاربر هنوز آنلاین است و در بازی نیست
            $playerFile = "data/user/{$item['user_id']}.json";
            if (file_exists($playerFile)) {
                $playerData = json_decode(file_get_contents($playerFile), true);
                // بررسی اینکه کاربر در بازی نباشد
                if (!isset($playerData['userfild']['ingame']) || $playerData['userfild']['ingame'] != 'on') {
                    $suitablePlayers[] = $item;
                    $suitableIndexes[] = $index;

                    // اگر 3 بازیکن پیدا شد، بازی را شروع کن
                    if (count($suitablePlayers) >= 3) {
                        break;
                    }
                }
            }
        }
    }

    // اگر 3 بازیکن مناسب پیدا شد
    if (count($suitablePlayers) >= 3) {
        // اضافه کردن کاربر فعلی به لیست بازیکنان
        array_unshift($suitablePlayers, $currentUser);
        array_unshift($suitableIndexes, $currentUserIndex);

        // حذف همه 4 بازیکن از صف
        $indexesToRemove = array_slice($suitableIndexes, 0, 4);
        rsort($indexesToRemove); // مرتب کردن از بزرگ به کوچک برای حذف صحیح

        foreach ($indexesToRemove as $indexToRemove) {
            unset($queue[$indexToRemove]);
        }

        file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));

        // شروع بازی 4 نفره
        startFourPlayerGame(array_slice($suitablePlayers, 0, 4));
        return true;
    }

    return false;
}

//تابع شروع بازی 4 نفره:
function startFourPlayerGame($players, $isFriendlyGame = false) {
    $playerIds = [];
    $playerNicknames = [];
    $playerTokens = [];

    // جمع‌آوری اطلاعات بازیکنان
    foreach ($players as $player) {
        $playerId = $player['user_id'];
        $playerIds[] = $playerId;

        // دریافت اطلاعات کامل بازیکن
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[] = !empty($playerData['userfild']['nickname']) ? $playerData['userfild']['nickname'] : "ناشناس";
        $playerTokens[] = isset($playerData['userfild']['user_token']) ? $playerData['userfild']['user_token'] : "نامشخص";
    }

    // کسر سکه از همه بازیکنان (2 سکه برای بازی عادی، بازی دوستانه رایگان است)
    $coinCost = 2; // پیش‌فرض برای بازی عادی

    $failedPlayers = [];

    // فقط در بازی‌های عادی (غیر دوستانه) سکه کسر می‌شود
    if (!$isFriendlyGame) {
        foreach ($playerIds as $playerId) {
            if (!hasActiveSubscription($playerId)) {
                if (!checkAndDeductCoins($playerId, $coinCost)) {
                    $failedPlayers[] = $playerId;
                }
            }
        }
    }

    // اگر کسی سکه کافی نداشت، همه را به صف برگردان
    if (!empty($failedPlayers)) {
        // بازگرداندن سکه بازیکنان موفق
        foreach ($playerIds as $playerId) {
            if (!in_array($playerId, $failedPlayers) && !hasActiveSubscription($playerId)) {
                refundCoins($playerId, $coinCost);
            }
        }

        // بازگرداندن همه به صف
        foreach ($players as $player) {
            addToFourPlayerQueue($player['user_id']);
        }
        return false;
    }

    // ایجاد گروه بازی
    $gameId = 'four_player_' . time() . '_' . rand(1000, 9999);
    createFourPlayerGameSession($gameId, $playerIds);

    // ارسال پیام به همه بازیکنان
    sendFourPlayerGameStartMessages($playerIds, $playerNicknames, $playerTokens, $gameId);

    // قرعه‌کشی برای تعیین نوبت اول
    $firstPlayerIndex = array_rand($playerIds);
    $firstPlayerId = $playerIds[$firstPlayerIndex];

    // ارسال پیام انتخاب جرعت/حقیقت به بازیکن اول
    sendFirstFourPlayerChoice($firstPlayerId, $gameId);

    return true;
}

//تابع ایجاد جلسه بازی 4 نفره:
function createFourPlayerGameSession($gameId, $playerIds) {
    $gameData = [
        'game_id' => $gameId,
        'players' => $playerIds,
        'player_count' => 4,
        'current_turn' => 0,
        'game_state' => 'waiting_for_choice',
        'created_at' => time(),
        'round' => 1,
        'current_questioner' => null,
        'current_answerer' => null,
        'current_observer1' => null,
        'current_observer2' => null,
        'current_choice' => null,
        'current_question' => null
    ];

    // ذخیره اطلاعات بازی
    if (!file_exists("data/four_player_games")) {
        mkdir("data/four_player_games", 0777, true);
    }

    file_put_contents("data/four_player_games/$gameId.json", json_encode($gameData, JSON_PRETTY_PRINT));

    // تنظیم وضعیت بازیکنان
    foreach ($playerIds as $playerId) {
        $userData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $userData['userfild']['ingame'] = 'on';
        $userData['userfild']['four_player_game_id'] = $gameId;
        file_put_contents("data/user/$playerId.json", json_encode($userData, true));

        // تنظیم زمان شروع بازی
        setGameStartTime($playerId);
    }
}

//تابع ارسال پیام شروع بازی 4 نفره:
function sendFourPlayerGameStartMessages($playerIds, $playerNicknames, $playerTokens, $gameId) {
    // ویرایش پیام جستجو به "پایان جستجو" برای همه بازیکنان
    foreach ($playerIds as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        if (isset($playerData['userfild']['four_player_search_message_id'])) {
            jijibot('editmessagetext',[
                'chat_id' => $playerId,
                'message_id' => $playerData['userfild']['four_player_search_message_id'],
                'text' => "🔔 پایان جستجو

<blockquote>⚠️ هشدار سیستم : کاربر گرامی به هیچ وجه اطلاعات تماس خود را در اختیار افراد قرار ندهید.</blockquote>",
                'parse_mode' => 'HTML'
            ]);
            unset($playerData['userfild']['four_player_search_message_id']);
            file_put_contents("data/user/$playerId.json", json_encode($playerData, true));
        }
    }

    $playersList = "";
    for ($i = 0; $i < count($playerIds); $i++) {
        $playersList .= "✱ " . $playerNicknames[$i] . " (<a href=\"https://t.me/jdarebot?start=" . $playerTokens[$i] . "\">" . $playerTokens[$i] . "</a>)\n";
    }

    $coinCost = $isFriendlyGame ? 4 : 2;
    $gameType = $isFriendlyGame ? "دوستانه " : "";

    foreach ($playerIds as $index => $playerId) {
        $hasSubscription = hasActiveSubscription($playerId);

        if ($isFriendlyGame) {
            $coinMessage = "🎁 بازی دوستانه رایگان است - سکه‌ای کسر نشد.";
        } else {
            $coinMessage = $hasSubscription ?
                "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
                "💰 $coinCost سکه از حساب شما کسر شد.";
        }

        jijibot('sendmessage',[
            'chat_id' => $playerId,
            'text' => "🔥 بازی {$gameType}4 نفره شروع شد!

👥 بازیکنان:
$playersList
✱ $coinMessage

✱ در حال پردازش بازی...

✱ ربات در حال قرعه کشی برای بازی می باشد.",
            'parse_mode' => 'HTML',
            'disable_web_page_preview' => true,
            'reply_markup' => json_encode([
                'keyboard' => [
                    [
                        ['text' => "👥 مشاهده بازیکنان"],
                        ['text' => "❌ ترک بازی"]
                    ],
                ],
                'resize_keyboard' => true
            ])
        ]);
    }
}

//تابع ارسال انتخاب به بازیکن اول (به‌روزرسانی شده):
function sendFirstFourPlayerChoice($playerId, $gameId) {
    // قرعه‌کشی برای تعیین نقش‌ها
    $roles = assignFourPlayerRoles($gameId);
    if (!$roles) return false;

    // ارسال پیام‌های نقش به همه بازیکنان
    sendFourPlayerRoleMessages($gameId, $roles);

    return true;
}

//تابع قرعه‌کشی برای تعیین نقش‌های بازیکنان در بازی 4 نفره:
function assignFourPlayerRoles($gameId) {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) {
        error_log("assignFourPlayerRoles: Game data not found for gameId: $gameId");
        return false;
    }

    $players = $gameData['players'];
    if (count($players) != 4) {
        error_log("assignFourPlayerRoles: Invalid player count: " . count($players));
        return false;
    }

    // قرعه‌کشی برای تعیین سوال‌کننده
    $questionerIndex = array_rand($players);
    $questioner = $players[$questionerIndex];

    // حذف سوال‌کننده از لیست برای انتخاب پاسخ‌دهنده
    $remainingPlayers = array_values(array_diff($players, [$questioner]));

    // قرعه‌کشی برای تعیین پاسخ‌دهنده از 3 نفر باقی‌مانده
    $answererIndex = array_rand($remainingPlayers);
    $answerer = $remainingPlayers[$answererIndex];

    // 2 نفر باقی‌مانده به عنوان مشاهده‌گران
    $observers = array_values(array_diff($remainingPlayers, [$answerer]));
    $observer1 = $observers[0];
    $observer2 = $observers[1];

    // به‌روزرسانی اطلاعات بازی
    $gameData['current_questioner'] = $questioner;
    $gameData['current_answerer'] = $answerer;
    $gameData['current_observer1'] = $observer1;
    $gameData['current_observer2'] = $observer2;
    $gameData['game_state'] = 'waiting_for_choice';
    $gameData['current_choice'] = null;
    $gameData['current_question'] = null;

    $updateResult = updateFourPlayerGameData($gameId, $gameData);
    if (!$updateResult) {
        error_log("assignFourPlayerRoles: Failed to update game data for gameId: $gameId");
        return false;
    }

    return [
        'questioner' => $questioner,
        'answerer' => $answerer,
        'observer1' => $observer1,
        'observer2' => $observer2
    ];
}

//تابع ارسال پیام‌های نقش به بازیکنان در بازی 4 نفره:
function sendFourPlayerRoleMessages($gameId, $roles) {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) return false;

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $questionerNickname = $playerNicknames[$roles['questioner']];
    $answererNickname = $playerNicknames[$roles['answerer']];
    $observer1Nickname = $playerNicknames[$roles['observer1']];
    $observer2Nickname = $playerNicknames[$roles['observer2']];

    // پیام برای سوال‌کننده
    jijibot('sendmessage',[
        'chat_id' => $roles['questioner'],
        'text' => "🎯 نقش شما: سوال‌کننده

⏳ منتظر انتخاب $answererNickname باشید...

✱ پاسخ‌دهنده: $answererNickname
✱ مشاهده‌گر 1: $observer1Nickname
✱ مشاهده‌گر 2: $observer2Nickname

✱ شما می‌توانید با مشاهده‌گران چت کنید (تا قبل از شروع سوال)
✱ بعد از انتخاب پاسخ‌دهنده، شما سوال خواهید پرسید.",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // پیام برای پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id' => $roles['answerer'],
        'text' => "🎯 نقش شما: پاسخ‌دهنده

✨ نوبت شما است که انتخاب کنید!

✱ سوال‌کننده: $questionerNickname
✱ مشاهده‌گر 1: $observer1Nickname
✱ مشاهده‌گر 2: $observer2Nickname

لطفا یکی را انتخاب کنید:",
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "💪🏻 جرعت", 'callback_data' => "four_dare_$gameId"],
                    ['text' => "🗣 حقیقت", 'callback_data' => "four_truth_$gameId"]
                ],
            ]
        ])
    ]);

    // پیام برای مشاهده‌گر 1
    jijibot('sendmessage',[
        'chat_id' => $roles['observer1'],
        'text' => "✨ نقش شما: مشاهده‌گر 1

👀 شما در این دور مشاهده‌گر هستید.

✱ سوال‌کننده: $questionerNickname
✱ پاسخ‌دهنده: $answererNickname
✱ مشاهده‌گر 2: $observer2Nickname

✱ شما می‌توانید با سوال‌کننده چت کنید
✱ منتظر انتخاب $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // پیام برای مشاهده‌گر 2
    jijibot('sendmessage',[
        'chat_id' => $roles['observer2'],
        'text' => "✨ نقش شما: مشاهده‌گر 2

👀 شما در این دور مشاهده‌گر هستید.

✱ سوال‌کننده: $questionerNickname
✱ پاسخ‌دهنده: $answererNickname
✱ مشاهده‌گر 1: $observer1Nickname

✱ شما می‌توانید با سوال‌کننده چت کنید
✱ منتظر انتخاب $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    return true;
}

//تابع دریافت اطلاعات بازی 4 نفره:
function getFourPlayerGameData($gameId) {
    $gameFile = "data/four_player_games/$gameId.json";
    if (!file_exists($gameFile)) {
        return null;
    }
    return json_decode(file_get_contents($gameFile), true);
}

//تابع به‌روزرسانی اطلاعات بازی 4 نفره:
function updateFourPlayerGameData($gameId, $gameData) {
    $gameFile = "data/four_player_games/$gameId.json";

    // بررسی وجود دایرکتوری
    if (!file_exists("data/four_player_games")) {
        mkdir("data/four_player_games", 0777, true);
    }

    $result = file_put_contents($gameFile, json_encode($gameData, JSON_PRETTY_PRINT));
    if ($result === false) {
        error_log("updateFourPlayerGameData: Failed to write game data for gameId: $gameId");
        return false;
    }

    return true;
}

//تابع بررسی اینکه آیا کاربر در بازی 4 نفره است:
function isUserInFourPlayerGame($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    return isset($userData['userfild']['four_player_game_id']) &&
           !empty($userData['userfild']['four_player_game_id']);
}

//تابع دریافت شناسه بازی 4 نفره کاربر:
function getUserFourPlayerGameId($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return null;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    return isset($userData['userfild']['four_player_game_id']) ?
           $userData['userfild']['four_player_game_id'] : null;
}

//تابع دریافت نقش کاربر در بازی 4 نفره:
function getUserRoleInFourPlayerGame($userId, $gameId) {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) return null;

    if ($userId == $gameData['current_questioner']) {
        return 'questioner';
    } elseif ($userId == $gameData['current_answerer']) {
        return 'answerer';
    } elseif ($userId == $gameData['current_observer1']) {
        return 'observer1';
    } elseif ($userId == $gameData['current_observer2']) {
        return 'observer2';
    }

    return null;
}

//تابع پایان بازی 4 نفره:
function endFourPlayerGame($gameId, $reason = 'completed') {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) return false;

    $playerIds = $gameData['players'];

    // تنظیم وضعیت بازیکنان
    foreach ($playerIds as $playerId) {
        $userFile = "data/user/$playerId.json";
        if (file_exists($userFile)) {
            $userData = json_decode(file_get_contents($userFile), true);
            $userData['userfild']['ingame'] = 'off';
            unset($userData['userfild']['four_player_game_id']);
            file_put_contents($userFile, json_encode($userData, true));
        }
    }

    // حذف فایل بازی
    $gameFile = "data/four_player_games/$gameId.json";
    if (file_exists($gameFile)) {
        unlink($gameFile);
    }

    return true;
}

//تابع ارسال پیام پایان بازی به همه بازیکنان:
function sendFourPlayerGameEndMessages($playerIds, $reason = 'completed', $leavingPlayerNickname = null) {
    foreach($playerIds as $playerId) {
        // بررسی زمان بازی برای بازگرداندن سکه
        $shouldRefundCoins = !isGameLongerThan2Minutes($playerId);

        // پیام اول: بستن دکمه‌ها + اطلاع‌رسانی سکه
        if ($shouldRefundCoins) {
            if (!hasActiveSubscription($playerId)) {
                refundCoins($playerId, 2);
                $coinMessage = "💳 سکه

به علت پایان 2 سکه مصرف شده در این بازی به حساب شما برگردانده شد.";
            } else {
                $coinMessage = "💳 سکه

به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشده بود.";
            }
        } else {
            $coinMessage = "💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.";
        }

        jijibot('sendmessage',[
            'chat_id'=>$playerId,
            'text'=>$coinMessage,
            'reply_markup'=>json_encode([
                'remove_keyboard'=>true
            ])
        ]);

        // پیام دوم: اعلام پایان بازی + دکمه منوی اصلی
        $endMessage = "";
        switch($reason) {
            case 'player_left':
                $endMessage = "🚪 بازی 4 نفره به دلیل خروج $leavingPlayerNickname پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
                break;
            case 'completed':
                $endMessage = "🎉 بازی 4 نفره با موفقیت به پایان رسید!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
                break;
            default:
                $endMessage = "🔚 بازی 4 نفره پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.";
        }

        jijibot('sendmessage',[
            'chat_id'=>$playerId,
            'text'=>$endMessage,
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                    ],
                ]
            ])
        ]);
    }
}

//تابع دریافت آمار بازی 4 نفره:
function getFourPlayerGameStats($gameId) {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) return null;

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    return [
        'round' => $gameData['round'],
        'state' => $gameData['game_state'],
        'questioner' => $playerNicknames[$gameData['current_questioner']] ?? 'نامشخص',
        'answerer' => $playerNicknames[$gameData['current_answerer']] ?? 'نامشخص',
        'observer1' => $playerNicknames[$gameData['current_observer1']] ?? 'نامشخص',
        'observer2' => $playerNicknames[$gameData['current_observer2']] ?? 'نامشخص',
        'choice' => $gameData['current_choice'] ?? null,
        'question' => $gameData['current_question'] ?? null
    ];
}

//تابع پاکسازی بازی‌های قدیمی (بیش از 30 دقیقه):
function cleanupOldFourPlayerGames() {
    $gamesDir = "data/four_player_games";
    if (!file_exists($gamesDir)) return;

    $files = glob("$gamesDir/*.json");
    $currentTime = time();
    $maxAge = 30 * 60; // 30 دقیقه

    foreach ($files as $file) {
        $gameData = json_decode(file_get_contents($file), true);
        if ($gameData && isset($gameData['created_at'])) {
            $gameAge = $currentTime - $gameData['created_at'];
            if ($gameAge > $maxAge) {
                // اطلاع‌رسانی به بازیکنان
                foreach ($gameData['players'] as $playerId) {
                    jijibot('sendmessage',[
                        'chat_id' => $playerId,
                        'text' => "⏰ بازی 4 نفره به دلیل عدم فعالیت پایان یافت!

بازی بیش از 30 دقیقه غیرفعال بود.",
                        'reply_markup' => json_encode([
                            'remove_keyboard' => true
                        ])
                    ]);

                    // پاک کردن اطلاعات بازی از کاربر
                    $userFile = "data/user/$playerId.json";
                    if (file_exists($userFile)) {
                        $userData = json_decode(file_get_contents($userFile), true);
                        $userData['userfild']['ingame'] = 'off';
                        $userData['userfild']['step'] = 'none';
                        unset($userData['userfild']['four_player_game_id']);
                        file_put_contents($userFile, json_encode($userData, true));
                    }
                }

                // حذف فایل بازی
                unlink($file);
            }
        }
    }
}

//تابع نمایش وضعیت فعلی بازی برای کاربر:
function showFourPlayerGameStatus($userId) {
    $gameId = getUserFourPlayerGameId($userId);
    if (!$gameId) {
        jijibot('sendmessage',[
            'chat_id' => $userId,
            'text' => "❌ شما در حال حاضر در بازی 4 نفره نیستید!",
        ]);
        return false;
    }

    $gameStats = getFourPlayerGameStats($gameId);
    if (!$gameStats) {
        jijibot('sendmessage',[
            'chat_id' => $userId,
            'text' => "❌ اطلاعات بازی یافت نشد!",
        ]);
        return false;
    }

    $userRole = getUserRoleInFourPlayerGame($userId, $gameId);
    $roleText = "";
    switch($userRole) {
        case 'questioner':
            $roleText = "🎯 سوال‌کننده";
            break;
        case 'answerer':
            $roleText = "🎯 پاسخ‌دهنده";
            break;
        case 'observer1':
            $roleText = "👀 مشاهده‌گر 1";
            break;
        case 'observer2':
            $roleText = "👀 مشاهده‌گر 2";
            break;
        default:
            $roleText = "❓ نامشخص";
    }

    $stateText = "";
    switch($gameStats['state']) {
        case 'waiting_for_choice':
            $stateText = "⏳ منتظر انتخاب جرعت/حقیقت";
            break;
        case 'waiting_for_question':
            $stateText = "⏳ منتظر سوال";
            break;
        case 'waiting_for_answer':
            $stateText = "⏳ منتظر پاسخ";
            break;
        default:
            $stateText = "❓ نامشخص";
    }

    $statusMessage = "📊 وضعیت بازی 4 نفره

🎮 دور: {$gameStats['round']}
$roleText

👥 بازیکنان:
🎯 سوال‌کننده: {$gameStats['questioner']}
🎯 پاسخ‌دهنده: {$gameStats['answerer']}
👀 مشاهده‌گر 1: {$gameStats['observer1']}
👀 مشاهده‌گر 2: {$gameStats['observer2']}

📋 وضعیت: $stateText";

    if ($gameStats['choice']) {
        $choiceText = $gameStats['choice'] == 'dare' ? 'جرعت' : 'حقیقت';
        $statusMessage .= "\n💪🏻 انتخاب شده: $choiceText";
    }

    jijibot('sendmessage',[
        'chat_id' => $userId,
        'text' => $statusMessage,
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    return true;
}

//تابع شروع دور جدید بازی 4 نفره:
function startNewFourPlayerRound($gameId) {
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) return false;

    // افزایش شماره دور
    $gameData['round']++;
    updateFourPlayerGameData($gameId, $gameData);

    // قرعه‌کشی جدید برای نقش‌ها
    $roles = assignFourPlayerRoles($gameId);
    if (!$roles) return false;

    // ارسال پیام شروع دور جدید
    foreach ($gameData['players'] as $playerId) {
        jijibot('sendmessage',[
            'chat_id' => $playerId,
            'text' => "🎲 دور جدید شروع شد!

🔄 در حال قرعه‌کشی برای تعیین نقش‌های جدید...

✱ دور: " . $gameData['round'],
            'reply_markup' => json_encode([
                'keyboard' => [
                    [
                        ['text' => "👥 مشاهده بازیکنان"],
                        ['text' => "❌ ترک بازی"]
                    ],
                ],
                'resize_keyboard' => true
            ])
        ]);
    }

    // ارسال پیام‌های نقش
    sendFourPlayerRoleMessages($gameId, $roles);

    return true;
}
