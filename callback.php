<?php
//-----------------------------------------------------------------------------------------
// فایل callback پرداخت - درگاه Zibal
// این فایل بعد از پرداخت توسط درگاه فراخوانی می‌شود
//-----------------------------------------------------------------------------------------

// تنظیمات ربات تلگرام
$botToken = "8155986826:AAGSIj-HFXAKhC4ikXjfHEVRgyIFFtU9lME";

// تنظیم debug mode
$debug = false;

// تنظیم خطاها
if ($debug) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// تابع ارسال پیام تلگرام
function jijibot($method, $datas = []) {
    global $botToken;
    $url = "https://api.telegram.org/bot{$botToken}/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

// کلاس Session ساده
class Session {
    public static function get($user_id, $key) {
        $sessionFile = "data/session/$user_id.json";
        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true);
            return isset($sessions[$key]) ? $sessions[$key] : null;
        }
        return null;
    }

    public static function set($user_id, $key, $value) {
        $sessionFile = "data/session/$user_id.json";

        // ایجاد پوشه session در صورت عدم وجود
        if (!file_exists("data/session")) {
            mkdir("data/session", 0777, true);
        }

        $sessions = [];
        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true);
        }
        $sessions[$key] = $value;
        file_put_contents($sessionFile, json_encode($sessions, true));
    }

    public static function delete($user_id, $key) {
        $sessionFile = "data/session/$user_id.json";
        if (file_exists($sessionFile)) {
            $sessions = json_decode(file_get_contents($sessionFile), true);
            if (isset($sessions[$key])) {
                unset($sessions[$key]);
                file_put_contents($sessionFile, json_encode($sessions, true));
            }
        }
    }
}

// تابع لاگ‌گیری
function logPayment($message) {
    // ایجاد پوشه logs در صورت عدم وجود
    if (!file_exists("data/logs")) {
        mkdir("data/logs", 0777, true);
    }

    $logFile = 'data/logs/callback_log.txt';
    $date = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$date] $message\n", FILE_APPEND);
}

/**
 * Function to connect to Zibal API
 */
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response);
}

// تابع فعال‌سازی اشتراک کاربر
function activateSubscription($user_id, $plan) {
    $now = time();
    $duration = 0;

    switch($plan) {
        case 'sub_30':
            $duration = 30 * 24 * 60 * 60;
            $plan_type = 'sub_30';
            break;
        case 'sub_60':
            $duration = 60 * 24 * 60 * 60;
            $plan_type = 'sub_60';
            break;
        case 'sub_90':
            $duration = 90 * 24 * 60 * 60;
            $plan_type = 'sub_90';
            break;
        case 'sub_365':
            $duration = 365 * 24 * 60 * 60; // 1 سال
            $plan_type = 'sub_365';
            break;
        default:
            $duration = 30 * 24 * 60 * 60; // 30 روز پیش‌فرض
            $plan_type = 'sub_30';
    }

    $userFile = "data/user/{$user_id}.json";

    if (file_exists($userFile)) {
        $userData = json_decode(file_get_contents($userFile), true);

        // بررسی اشتراک فعلی و محاسبه تاریخ پایان
        if (isset($userData['userfild']['subscription']) &&
            isset($userData['userfild']['subscription']['is_active']) &&
            $userData['userfild']['subscription']['is_active'] &&
            isset($userData['userfild']['subscription']['end_date']) &&
            $userData['userfild']['subscription']['end_date'] > $now) {

            // اگر اشتراک فعال دارد و هنوز منقضی نشده، روزهای جدید را به روزهای باقی‌مانده اضافه کن
            $end_date = $userData['userfild']['subscription']['end_date'] + $duration;
            $remaining_days = ceil(($userData['userfild']['subscription']['end_date'] - $now) / (24 * 60 * 60));
            $new_days = $duration / (24 * 60 * 60);
            logPayment("User $user_id had $remaining_days days remaining, adding $new_days new days");
        } else {
            // اگر اشتراک فعال ندارد یا منقضی شده، از الان شروع کن
            $end_date = $now + $duration;
            logPayment("User $user_id starting new subscription from now");
        }

        // به‌روزرسانی اطلاعات اشتراک
        $userData['userfild']['subscription'] = [
            'type' => $plan_type,
            'start_date' => $now,
            'end_date' => $end_date,
            'is_active' => true,
            'purchase_date' => date('Y-m-d H:i:s')
        ];

        // فعال کردن کاربر
        $userData['userfild']['is_active'] = true;

        // ذخیره تغییرات
        file_put_contents($userFile, json_encode($userData, JSON_PRETTY_PRINT));

        logPayment("User $user_id subscription activated: $plan_type until " . date('Y-m-d H:i:s', $end_date));
        return true;
    } else {
        logPayment("User file not found for user_id: $user_id");
        return false;
    }
}

/**
 * Get plan name for display
 */
function getPlanName($plan) {
    switch($plan) {
        case 'sub_30':
            return 'اشتراک 30 روزه';
        case 'sub_60':
            return 'اشتراک 60 روزه';
        case 'sub_90':
            return 'اشتراک 90 روزه';
        case 'sub_365':
            return 'اشتراک 1 ساله';
        default:
            return 'نامشخص';
    }
}

// Start processing the callback
logPayment("=== CALLBACK START ===");
logPayment("GET parameters: " . json_encode($_GET));
logPayment("POST parameters: " . json_encode($_POST));

// اگر debug mode خاموش است، خطاها را مخفی کن
if (!$debug) {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Check if payment was successful - first check for all required parameters
if (isset($_GET['trackId']) && isset($_GET['orderId'])) {
    $trackId = $_GET['trackId'];
    $orderId = $_GET['orderId'];
    $success = $_GET['success'] ?? '0'; // This could be 0 or 1
    $status = $_GET['status'] ?? '0'; // Status from gateway

    logPayment("Payment parameters - success: $success, status: $status, trackId: $trackId, orderId: $orderId");
    
    // Extract user ID from order ID (format: SPX-TIMESTAMP-USERID)
    if (preg_match('/SPX-\d+-(\d+)/', $orderId, $matches)) {
        $user_id = $matches[1];
        logPayment("Extracted user_id: $user_id from orderId: $orderId using new format");
    } else if (preg_match('/(\d+)$/', $orderId, $matches)) {
        // Fallback to old format
        $user_id = $matches[1];
        logPayment("Extracted user_id: $user_id from orderId: $orderId using fallback format");
    } else {
        $user_id = '0';
        logPayment("Failed to extract user_id from orderId: $orderId");
    }
    
    // Always verify payment with Zibal regardless of success parameter
    $parameters = array(
        "merchant" => "zibal", // کلید مرچنت واقعی
        "trackId" => $trackId
    );
    
    logPayment("Sending verification request for trackId: $trackId with merchant key");
    $response = postToZibal('verify', $parameters);
    logPayment("Verification response: " . json_encode($response));

    // Check if we got a valid response
    if ($response) {
        // For debugging - log all verification details
        $resultCode = $response->result ?? 'undefined';
        logPayment("Result code: " . $resultCode);

        // بررسی پیام خطا برای تشخیص نوع کد 203
        if ($resultCode == 203) {
            $message = $response->message ?? '';
            logPayment("Code 203 received with message: " . $message);

            // اگر پیام "تراکنش قبلا تایید شده" باشد، موفق در نظر بگیر
            if (strpos($message, 'تایید شده') !== false || strpos($message, 'verified') !== false) {
                logPayment("Transaction already verified - treating as successful");
                $response->result = 100;
            } else {
                logPayment("Code 203 with different meaning: " . $message);
            }
        }
        
        // Check verification result (100 means success)
        if (isset($response->result) && $response->result == 100) {
            // Payment is verified successful by Zibal
            logPayment("Payment verification successful - result code: 100");

            // Get plan from session
            $plan = Session::get($user_id, 'payment_plan');
            logPayment("Retrieved plan from session: " . ($plan ?: 'null'));

            // Add additional debugging for payment amount to plan correlation
            $amount = $response->amount ?? 0;
            logPayment("Payment amount: " . $amount . " rials");

            // Try to determine plan from amount if session plan is missing
            if (!$plan) {
                // Convert from rials to toman
                $tomanAmount = $amount / 10;
                logPayment("Payment amount in toman: " . $tomanAmount);

                // Match amount to plan (قیمت‌های سیستم شما)
                if ($tomanAmount == 21000) {
                    $plan = 'sub_30';
                    logPayment("Determined plan from amount: sub_30");
                } else if ($tomanAmount == 42000) {
                    $plan = 'sub_60';
                    logPayment("Determined plan from amount: sub_60");
                } else if ($tomanAmount == 63000) {
                    $plan = 'sub_90';
                    logPayment("Determined plan from amount: sub_90");
                } else if ($tomanAmount == 252000) {
                    $plan = 'sub_365';
                    logPayment("Determined plan from amount: sub_365");
                } else {
                    // Default to 30 days if plan not found
                    $plan = 'sub_30';
                    logPayment("Using default plan: $plan for user $user_id (amount: $tomanAmount toman didn't match any plan)");
                }
            }
            
            // بررسی اینکه آیا این یک هدیه است یا نه
            $giftTargetUser = Session::get($user_id, 'gift_target_user');
            $giftTargetNickname = Session::get($user_id, 'gift_target_nickname');

            if ($giftTargetUser) {
                // این یک هدیه اشتراک است
                logPayment("Processing gift subscription for user $user_id to target $giftTargetUser with plan $plan");
                $activated = activateSubscription($giftTargetUser, $plan);

                if ($activated) {
                    // ارسال پیام موفقیت به خریدار
                    logPayment("Sending gift success message to buyer $user_id");
                    jijibot('sendmessage', [
                        'chat_id' => $user_id,
                        'text' => "🎁 هدیه اشتراک با موفقیت ارسال شد!\n\n✅ " . getPlanName($plan) . " به کاربر $giftTargetNickname هدیه داده شد.\n\n✱ جزئیات تراکنش:\n✱ شناسه پیگیری: <code>$trackId</code>\n✱ مبلغ: " . ($response->amount / 10) . " تومان\n✱ تاریخ فعال‌سازی: " . date('Y/m/d H:i') . "\n\nبا تشکر از سخاوت شما 🌹",
                        'parse_mode' => 'HTML',
                        'reply_markup' => json_encode([
                            'inline_keyboard' => [
                                [
                                    ['text' => '🏠 بازگشت به منوی اصلی', 'callback_data' => 'back']
                                ]
                            ]
                        ])
                    ]);

                    // ارسال پیام اطلاع‌رسانی به گیرنده هدیه
                    $senderNickname = Session::get($user_id, 'sender_nickname') ?: "کاربری";

                    // خواندن توکن فرستنده از فایل کاربری
                    $senderToken = "نامشخص";
                    $senderUserFile = "data/user/$user_id.json";
                    if (file_exists($senderUserFile)) {
                        $senderUserData = json_decode(file_get_contents($senderUserFile), true);
                        $senderToken = isset($senderUserData['userfild']['user_token']) ? $senderUserData['userfild']['user_token'] : "نامشخص";
                    }

                    // ترکیب نام و توکن (توکن به صورت لینک)
                    $senderInfo = "$senderNickname (<a href=\"https://t.me/jdarebot?start=$senderToken\">$senderToken</a>)";

                    // تعیین نوع اشتراک بر اساس پلن
                    $subscriptionType = "";
                    switch($plan) {
                        case 'sub_30':
                            $subscriptionType = "30روزه";
                            break;
                        case 'sub_60':
                            $subscriptionType = "60روزه";
                            break;
                        case 'sub_90':
                            $subscriptionType = "90روزه";
                            break;
                        case 'sub_365':
                            $subscriptionType = "1ساله";
                            break;
                        default:
                            $subscriptionType = "نامشخص";
                    }

                    jijibot('sendmessage', [
                        'chat_id' => $giftTargetUser,
                        'text' => "🎁 شما یک هدیه دریافت کردید!\n\n✱ فرستنده : $senderInfo\n✱ هدیه : اشتراک\n✱ نوع اشتراک : $subscriptionType\n✱ تاریخ فعال‌سازی: " . date('Y/m/d H:i') . "\n\nلذت ببرید! 🌟",
                        'parse_mode' => 'HTML',
                        'disable_web_page_preview' => true,
                        'reply_markup' => json_encode([
                            'inline_keyboard' => [
                                [
                                    ['text' => '👤 مشاهده فرستنده', 'callback_data' => "view_profile_$user_id", 'url' => '']
                                ]
                            ]
                        ])
                    ]);

                    // پاک کردن اطلاعات هدیه از سشن
                    Session::delete($user_id, 'gift_plan');
                    Session::delete($user_id, 'gift_target_user');
                    Session::delete($user_id, 'gift_target_nickname');
                    Session::delete($user_id, 'sender_nickname');
                    Session::delete($user_id, 'selected_plan');
                    Session::delete($user_id, 'plan_days');
                    Session::delete($user_id, 'plan_price');
                } else {
                    logPayment("Error activating gift subscription for target user $giftTargetUser");
                    jijibot('sendmessage', [
                        'chat_id' => $user_id,
                        'text' => "❌ <b>خطا در فعال‌سازی هدیه اشتراک</b>\n\nپرداخت شما موفقیت‌آمیز بود اما در فعال‌سازی اشتراک برای گیرنده خطایی رخ داد.\n\n📞 لطفاً با پشتیبانی تماس بگیرید:\n🔹 شناسه پیگیری: <code>$trackId</code>",
                        'parse_mode' => 'HTML'
                    ]);

                    // پاک کردن اطلاعات هدیه از سشن حتی در صورت خطا
                    Session::delete($user_id, 'gift_plan');
                    Session::delete($user_id, 'gift_target_user');
                    Session::delete($user_id, 'gift_target_nickname');
                    Session::delete($user_id, 'sender_nickname');
                    Session::delete($user_id, 'selected_plan');
                    Session::delete($user_id, 'plan_days');
                    Session::delete($user_id, 'plan_price');
                }
            } else {
                // اشتراک عادی برای خود کاربر
                logPayment("Activating subscription for user $user_id with plan $plan");
                $activated = activateSubscription($user_id, $plan);

                if ($activated) {
                    // Send success message to user
                    logPayment("Sending success message to user $user_id");
                    jijibot('sendmessage', [
                        'chat_id' => $user_id,
                        'text' => "🎉 پرداخت موفقیت‌آمیز!\n\n✅ " . getPlanName($plan) . " شما با موفقیت فعال شد.\n\n✱ جزئیات تراکنش:\n✱ شناسه پیگیری: <code>$trackId</code>\n✱ مبلغ: " . ($response->amount / 10) . " تومان\n✱ تاریخ فعال‌سازی: " . date('Y/m/d H:i') . "\n\nبا تشکر از اعتماد شما 🌹",
                        'parse_mode' => 'HTML',
                        'reply_markup' => json_encode([
                            'inline_keyboard' => [
                                [
                                    ['text' => '🏠 بازگشت به منوی اصلی', 'callback_data' => 'back']
                                ]
                            ]
                        ])
                    ]);

                    // پاک کردن تمام سشن‌های مربوط به پرداخت (هدیه و عادی)
                    Session::delete($user_id, 'gift_plan');
                    Session::delete($user_id, 'gift_target_user');
                    Session::delete($user_id, 'gift_target_nickname');
                    Session::delete($user_id, 'sender_nickname');
                    Session::delete($user_id, 'selected_plan');
                    Session::delete($user_id, 'plan_days');
                    Session::delete($user_id, 'plan_price');
                } else {
                    logPayment("Error activating subscription for user $user_id");
                    jijibot('sendmessage', [
                        'chat_id' => $user_id,
                        'text' => "❌ <b>خطا در فعال‌سازی اشتراک</b>\n\nپرداخت شما موفقیت‌آمیز بود اما در فعال‌سازی اشتراک خطایی رخ داد.\n\n📞 لطفاً با پشتیبانی تماس بگیرید:\n🔹 شناسه پیگیری: <code>$trackId</code>",
                        'parse_mode' => 'HTML'
                    ]);

                    // پاک کردن تمام سشن‌های مربوط به پرداخت حتی در صورت خطا
                    Session::delete($user_id, 'gift_plan');
                    Session::delete($user_id, 'gift_target_user');
                    Session::delete($user_id, 'gift_target_nickname');
                    Session::delete($user_id, 'sender_nickname');
                    Session::delete($user_id, 'selected_plan');
                    Session::delete($user_id, 'plan_days');
                    Session::delete($user_id, 'plan_price');
                }
            }

            if ($activated) {
                // Log successful payment
                logPayment("Payment successful for user $user_id, plan: $plan, amount: " . ($response->amount ?? 'unknown'));

                // Clear payment data from session
                Session::delete($user_id, 'payment_plan');
                Session::delete($user_id, 'payment_order_id');

                // Redirect to success page
                header('Location: https://speedx-team.ir/Payment/success.php');
                exit;

            } else {
                logPayment("Error activating subscription for user $user_id");

                // Send error message to user
                jijibot('sendmessage', [
                    'chat_id' => $user_id,
                    'text' => "❌ <b>خطا در فعال‌سازی اشتراک</b>\n\nپرداخت شما موفقیت‌آمیز بود اما در فعال‌سازی اشتراک خطایی رخ داد.\n\n📞 لطفاً با پشتیبانی تماس بگیرید:\n🔹 شناسه پیگیری: <code>$trackId</code>",
                    'parse_mode' => 'HTML'
                ]);

                // Redirect to error page
                header('Location: https://speedx-team.ir/Payment/error.php?error=activation_failed');
                exit;
            }
        } else {
            // Payment verification failed - Log the error code
            $errorCode = $response->result ?? 'unknown';
            logPayment("Payment verification failed with error code: $errorCode");
            
            if ($user_id && $user_id != '0') {
                jijibot('sendmessage', [
                    'chat_id' => $user_id,
                    'text' => "❌ پرداخت ناموفق\n\nمتأسفانه پرداخت شما تأیید نشد.\n\n✱ کد خطا: <code>$errorCode</code>\n✱ در صورت کسر وجه از حساب شما، مبلغ ظرف 72 ساعت بازگردانده خواهد شد.\n\n🔄 برای تلاش مجدد، از منو اشتراک اقدام کنید.",
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => '🔄 تلاش مجدد', 'callback_data' => 'subscription']
                            ]
                        ]
                    ])
                ]);
            }

            // Clear payment data from session
            Session::delete($user_id, 'payment_plan');
            Session::delete($user_id, 'payment_order_id');

            // Redirect to error page
            header('Location: https://speedx-team.ir/Payment/error.php?error=verification_failed&code=' . $errorCode);
            exit;
        }
    } else {
        // No response from Zibal API
        logPayment("No response from Zibal verification API");
        if ($user_id && $user_id != '0') {
            jijibot('sendmessage', [
                'chat_id' => $user_id,
                'text' => "❌ خطا در ارتباط با درگاه پرداخت!\n\nمتاسفانه امکان بررسی وضعیت پرداخت شما وجود ندارد. اگر مبلغی از حساب شما کسر شده، لطفا با پشتیبانی تماس بگیرید.",
                'parse_mode' => 'HTML',
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => '🔄 تلاش مجدد', 'callback_data' => 'subscription']
                        ]
                    ]
                ])
            ]);
        }
        
        // Redirect to error page
        if (!$debug) {
            header('Location: https://speedx-team.ir/Payment/error.php?error=api_error');
            exit;
        }
    }
} else {
    // Missing required parameters
    logPayment("Missing required parameters. Received: " . json_encode($_GET));
    
    // Try to extract user_id if orderId exists
    if (isset($_GET['orderId'])) {
        $orderId = $_GET['orderId'];
        
        // Extract user ID more carefully
        preg_match('/(\d+)$/', $orderId, $matches);
        $user_id = isset($matches[1]) ? $matches[1] : '0';
        
        if ($user_id && $user_id != '0') {
            // Send failure message to user
            jijibot('sendmessage', [
                'chat_id' => $user_id,
                'text' => "❌ پرداخت ناموفق!\n\nمتاسفانه پرداخت شما با موفقیت انجام نشد. می‌توانید مجدداً تلاش کنید یا با پشتیبانی تماس بگیرید.",
                'parse_mode' => 'HTML',
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => '🔄 تلاش مجدد', 'callback_data' => 'subscription']
                        ]
                    ]
                ])
            ]);
        }
        
        logPayment("Payment failed for orderId: $orderId, user_id: $user_id");
    } else {
        logPayment("Invalid callback request without orderId: " . json_encode($_GET));
    }
    
    // Redirect to error page
    if (!$debug) {
        header('Location: https://speedx-team.ir/Payment/error.php?error=missing_parameters');
        exit;
    }
}

// پایان پردازش callback
logPayment("=== CALLBACK END ===");

// اگر تا اینجا رسیده، یعنی هیچ redirect انجام نشده
// این حالت نباید اتفاق بیفتد
if ($debug) {
    echo "DEBUG: Callback processing completed without redirect";
} else {
    // در حالت عادی، هیچ خروجی نداشته باش
    http_response_code(200);
    exit;
}
?>